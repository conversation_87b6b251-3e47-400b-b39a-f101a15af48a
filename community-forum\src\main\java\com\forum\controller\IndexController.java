package com.forum.controller;

import com.forum.service.CategoryService;
import com.forum.service.PostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 主页面控制器
 */
@Controller
public class IndexController {
    
    @Autowired
    private CategoryService categoryService;
    
    @Autowired
    private PostService postService;
    
    /**
     * 首页
     */
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("categories", categoryService.getEnabledCategories());
        model.addAttribute("pageTitle", "社区论坛 - 首页");
        return "index";
    }
    
    /**
     * 分类页面
     */
    @GetMapping("/category/{categoryId}")
    public String category(@PathVariable Integer categoryId, Model model) {
        model.addAttribute("category", categoryService.getCategoryById(categoryId));
        model.addAttribute("categories", categoryService.getEnabledCategories());
        model.addAttribute("pageTitle", "社区论坛 - " + categoryService.getCategoryById(categoryId).getName());
        return "category";
    }
    
    /**
     * 帖子详情页面
     */
    @GetMapping("/post/{postId}")
    public String post(@PathVariable Long postId, Model model) {
        model.addAttribute("post", postService.getPostById(postId));
        model.addAttribute("categories", categoryService.getEnabledCategories());
        model.addAttribute("pageTitle", "社区论坛 - " + postService.getPostById(postId).getTitle());
        return "post";
    }
    
    /**
     * 搜索页面
     */
    @GetMapping("/search")
    public String search(@RequestParam String keyword, Model model) {
        model.addAttribute("keyword", keyword);
        model.addAttribute("categories", categoryService.getEnabledCategories());
        model.addAttribute("pageTitle", "社区论坛 - 搜索: " + keyword);
        return "search";
    }
    
    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String login(Model model) {
        model.addAttribute("pageTitle", "社区论坛 - 登录");
        return "login";
    }
    
    /**
     * 注册页面
     */
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("pageTitle", "社区论坛 - 注册");
        return "register";
    }
    
    /**
     * 用户中心页面
     */
    @GetMapping("/user/{userId}")
    public String userCenter(@PathVariable Long userId, Model model) {
        model.addAttribute("categories", categoryService.getEnabledCategories());
        model.addAttribute("pageTitle", "社区论坛 - 用户中心");
        return "user";
    }
    
    /**
     * 发布帖子页面
     */
    @GetMapping("/publish")
    public String publish(Model model) {
        model.addAttribute("categories", categoryService.getEnabledCategories());
        model.addAttribute("pageTitle", "社区论坛 - 发布帖子");
        return "publish";
    }
    
    /**
     * 编辑帖子页面
     */
    @GetMapping("/edit/{postId}")
    public String edit(@PathVariable Long postId, Model model) {
        model.addAttribute("post", postService.getPostById(postId));
        model.addAttribute("categories", categoryService.getEnabledCategories());
        model.addAttribute("pageTitle", "社区论坛 - 编辑帖子");
        return "edit";
    }
    
    /**
     * 静态资源CSS
     */
    @GetMapping("/static/css/style.css")
    public String getStyleCss() {
        return "forward:/WEB-INF/resources/css/style.css";
    }
    
    /**
     * 静态资源JS
     */
    @GetMapping("/static/js/main.js")
    public String getMainJs() {
        return "forward:/WEB-INF/resources/js/main.js";
    }
    
    /**
     * 默认头像
     */
    @GetMapping("/static/images/avatar/default.jpg")
    public String getDefaultAvatar() {
        return "forward:/WEB-INF/resources/images/avatar/default.jpg";
    }
} 