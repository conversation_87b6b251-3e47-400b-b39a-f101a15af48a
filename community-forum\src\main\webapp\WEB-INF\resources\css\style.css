/* 全局样式 */
:root {
    --primary-color: #4a76a8;
    --secondary-color: #eaeaea;
    --accent-color: #3a5795;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --bg-color: #f8f8f8;
    --card-bg: #fff;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 头部导航 */
.header {
    background-color: var(--primary-color);
    padding: 15px 0;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.search-form {
    display: flex;
    flex-grow: 1;
    max-width: 500px;
    margin: 0 20px;
}

.search-input {
    width: 100%;
    padding: 8px 15px;
    border: none;
    border-radius: 20px 0 0 20px;
    font-size: 14px;
}

.search-btn {
    background-color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 0 20px 20px 0;
    cursor: pointer;
}

.nav-links {
    display: flex;
    align-items: center;
}

.user-menu {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.notification-badge {
    position: relative;
    margin-right: 15px;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 50%;
}

.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
}

.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: var(--card-bg);
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    z-index: 1;
    border-radius: 4px;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    color: var(--text-color);
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    background-color: var(--secondary-color);
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    display: inline-block;
    margin-left: 10px;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
    border: none;
}

.btn-outline {
    background-color: transparent;
    color: white;
    border: 1px solid white;
}

/* 主要内容区 */
.main {
    display: flex;
    margin-top: 30px;
    margin-bottom: 30px;
}

.content {
    flex-grow: 1;
    margin-right: 20px;
}

.sidebar {
    width: 300px;
}

/* 帖子卡片 */
.post-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
}

.post-header {
    display: flex;
    margin-bottom: 15px;
}

.user-name {
    font-weight: 600;
    color: var(--text-color);
}

.post-meta {
    font-size: 12px;
    color: var(--light-text);
    margin-top: 3px;
}

.post-category {
    margin-left: 10px;
    padding: 2px 8px;
    background-color: var(--secondary-color);
    border-radius: 10px;
    color: var(--text-color);
}

.post-title {
    font-size: 18px;
    margin-bottom: 10px;
}

.post-excerpt {
    color: var(--light-text);
    margin-bottom: 15px;
}

.post-footer {
    display: flex;
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.action {
    display: flex;
    align-items: center;
    margin-right: 20px;
    color: var(--light-text);
    font-size: 14px;
}

.action i {
    margin-right: 5px;
}

.like-button, .favorite-button {
    cursor: pointer;
}

.like-button:hover, .favorite-button:hover {
    color: var(--primary-color);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination a {
    color: var(--primary-color);
    padding: 8px 14px;
    text-decoration: none;
    border: 1px solid var(--border-color);
    margin: 0 4px;
    border-radius: 4px;
}

.pagination a.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination a.disabled {
    color: var(--light-text);
    cursor: not-allowed;
}

/* 侧边栏 */
.sidebar-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.sidebar-header {
    padding: 15px;
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

.sidebar-header i {
    margin-right: 5px;
}

.sidebar-content {
    padding: 15px;
}

.category-list {
    list-style: none;
}

.category-list li {
    margin-bottom: 10px;
}

.category-list a {
    display: flex;
    justify-content: space-between;
    color: var(--text-color);
    text-decoration: none;
}

.category-list a:hover {
    color: var(--primary-color);
}

.category-list .icon {
    margin-right: 8px;
}

.count {
    background-color: var(--secondary-color);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
}

.sidebar-post {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-post:last-child {
    border-bottom: none;
}

.sidebar-post-title {
    font-weight: 500;
    margin-bottom: 5px;
    display: block;
}

.sidebar-post-meta {
    display: flex;
    font-size: 12px;
    color: var(--light-text);
}

.sidebar-post-meta span {
    margin-right: 10px;
}

/* 页脚 */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 30px 0;
    margin-top: 50px;
}

.footer-content {
    text-align: center;
}

/* 加载中和错误提示 */
.loading-spinner {
    text-align: center;
    padding: 20px;
    color: var(--light-text);
}

.error-message {
    text-align: center;
    padding: 20px;
    color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main {
        flex-direction: column;
    }

    .content {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .sidebar {
        width: 100%;
    }

    .search-form {
        display: none;
    }
} 