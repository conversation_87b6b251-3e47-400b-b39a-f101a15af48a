/**
 * 社区论坛主要JavaScript文件
 */

// 全局变量
const API_BASE_URL = '/community-forum/api';
let currentUser = null;

// 登录函数
function login(username, password) {
    fetch(`${API_BASE_URL}/user/login`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 保存令牌和用户信息
            localStorage.setItem('token', data.data.token);
            localStorage.setItem('user', JSON.stringify(data.data.user));
            
            // 跳转到首页
            window.location.href = '/community-forum/';
        } else {
            alert(data.message || '登录失败，请检查用户名和密码');
        }
    })
    .catch(error => {
        console.error('登录失败:', error);
        alert('登录失败，请稍后再试');
    });
}

// 注册函数
function register(username, password, email, nickname) {
    fetch(`${API_BASE_URL}/user/register`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            password: password,
            email: email,
            nickname: nickname
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            alert('注册成功，请登录');
            window.location.href = '/community-forum/login';
        } else {
            alert(data.message || '注册失败，请检查输入信息');
        }
    })
    .catch(error => {
        console.error('注册失败:', error);
        alert('注册失败，请稍后再试');
    });
}

// 登出函数
function logout() {
    // 清除本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // 跳转到首页
    window.location.href = '/community-forum/';
}

// 获取未读通知数
function updateNotificationCount() {
    const token = localStorage.getItem('token');
    if (!token) return;
    
    fetch(`${API_BASE_URL}/notification/unread/count`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            const count = data.data;
            const badge = document.querySelector('.notification-badge .badge');
            
            if (badge) {
                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count;
                    badge.style.display = 'block';
                } else {
                    badge.style.display = 'none';
                }
            }
        }
    })
    .catch(error => console.error('获取通知数失败:', error));
}

// 点赞帖子
function likePost(postId) {
    const token = localStorage.getItem('token');
    if (!token) {
        alert('请先登录');
        window.location.href = '/community-forum/login';
        return;
    }
    
    fetch(`${API_BASE_URL}/post/like/${postId}`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 更新点赞数
            const likeCount = document.querySelector(`.like-button[data-post-id="${postId}"] .like-count`);
            if (likeCount) {
                likeCount.textContent = data.data.likeCount;
            }
        } else {
            alert(data.message || '操作失败');
        }
    })
    .catch(error => {
        console.error('点赞失败:', error);
        alert('操作失败，请稍后再试');
    });
}

// 收藏帖子
function favoritePost(postId) {
    const token = localStorage.getItem('token');
    if (!token) {
        alert('请先登录');
        window.location.href = '/community-forum/login';
        return;
    }
    
    fetch(`${API_BASE_URL}/post/favorite/${postId}`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 更新收藏数
            const favoriteCount = document.querySelector(`.favorite-button[data-post-id="${postId}"] .favorite-count`);
            if (favoriteCount) {
                favoriteCount.textContent = data.data.favoriteCount;
            }
        } else {
            alert(data.message || '操作失败');
        }
    })
    .catch(error => {
        console.error('收藏失败:', error);
        alert('操作失败，请稍后再试');
    });
}

// 页面加载完成后添加事件监听
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有点赞按钮并添加事件监听
    const likeButtons = document.querySelectorAll('.like-button');
    likeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const postId = this.getAttribute('data-post-id');
            likePost(postId);
        });
    });
    
    // 获取所有收藏按钮并添加事件监听
    const favoriteButtons = document.querySelectorAll('.favorite-button');
    favoriteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const postId = this.getAttribute('data-post-id');
            favoritePost(postId);
        });
    });
}); 